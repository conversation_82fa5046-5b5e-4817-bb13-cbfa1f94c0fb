import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Chip,
  Grid,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Cable as CableIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import responsabiliService from '../../services/responsabiliService';

const CreaComandaConCavi = ({
  cantiereId,
  open,
  onClose,
  onSuccess,
  tipoComandaPreselezionato = null,
  caviPreselezionati = []
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Step 1: Selezione tipo comanda
  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');

  // Aggiorna il tipo comanda quando cambia il prop
  useEffect(() => {
    if (tipoComandaPreselezionato) {
      setTipoComanda(tipoComandaPreselezionato);
    }
  }, [tipoComandaPreselezionato]);
  
  // Step 2: Selezione cavi
  const [caviDisponibili, setCaviDisponibili] = useState([]);
  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);
  const [loadingCavi, setLoadingCavi] = useState(false);

  // Step 3: Dettagli comanda
  const [formData, setFormData] = useState({
    responsabile: '',
    responsabile_email: '',
    responsabile_telefono: '',
    data_scadenza: '',
    numero_componenti_squadra: 1,
    note_capo_cantiere: ''
  });

  // Gestione responsabili
  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);
  const [loadingResponsabili, setLoadingResponsabili] = useState(false);

  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];

  // Carica cavi disponibili quando cambia il tipo comanda
  useEffect(() => {
    if (open && tipoComanda) {
      loadCaviDisponibili();
    }
  }, [open, tipoComanda]);

  // Carica responsabili disponibili quando si apre il dialog
  useEffect(() => {
    if (open && cantiereId) {
      loadResponsabiliDisponibili();
    }
  }, [open, cantiereId]);

  // Se ci sono cavi preselezionati, salta al passo dei dettagli
  useEffect(() => {
    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {
      setActiveStep(2); // Vai direttamente ai dettagli
      setCaviSelezionati(caviPreselezionati); // Imposta i cavi preselezionati
    }
  }, [open, caviPreselezionati, tipoComandaPreselezionato]);

  const loadCaviDisponibili = async () => {
    try {
      setLoadingCavi(true);
      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);
      setCaviDisponibili(response.cavi_disponibili || []);

      // Non resettare la selezione se ci sono cavi preselezionati
      if (caviPreselezionati.length === 0) {
        setCaviSelezionati([]);
      }

      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento dei cavi:', err);
      setError('Errore nel caricamento dei cavi disponibili');
    } finally {
      setLoadingCavi(false);
    }
  };

  const loadResponsabiliDisponibili = async () => {
    try {
      setLoadingResponsabili(true);
      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabiliDisponibili(responsabili || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      // Non mostrare errore per i responsabili, è opzionale
    } finally {
      setLoadingResponsabili(false);
    }
  };

  const handleNext = () => {
    if (activeStep === 1 && caviSelezionati.length === 0) {
      setError('Seleziona almeno un cavo');
      return;
    }
    if (activeStep === 2) {
      if (!formData.responsabile.trim()) {
        setError('Seleziona un responsabile dall\'elenco');
        return;
      }
    }
    setError(null);
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleCavoToggle = (cavo) => {
    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);
    if (isSelected) {
      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));
    } else {
      setCaviSelezionati([...caviSelezionati, cavo]);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Inizio creazione comanda:', {
        tipoComanda,
        responsabile: formData.responsabile,
        numeroCavi: caviSelezionati.length
      });

      const comandaData = {
        tipo_comanda: tipoComanda,
        descrizione: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,
        responsabile: formData.responsabile,
        responsabile_email: formData.responsabile_email || null,
        responsabile_telefono: formData.responsabile_telefono || null,
        data_scadenza: formData.data_scadenza || null,
        numero_componenti_squadra: formData.numero_componenti_squadra || 1,
        note_capo_cantiere: formData.note_capo_cantiere
      };

      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);

      console.log('📋 Dati comanda preparati:', comandaData);
      console.log('🔗 Cavi da assegnare:', listaIdCavi);

      const response = await comandeService.createComandaConCavi(
        cantiereId,
        comandaData,
        listaIdCavi
      );

      console.log('✅ Comanda creata con successo:', response);

      // Mostra messaggio di successo specifico per tipo comanda
      let successMessage = `Comanda ${getTipoComandaLabel(tipoComanda)} creata con successo!`;
      if (tipoComanda === 'POSA') {
        successMessage += ` I ${caviSelezionati.length} cavi sono ora "In corso" e assegnati a ${formData.responsabile}.`;
      }

      console.log('📢 Messaggio successo:', successMessage);

      onSuccess && onSuccess(response, successMessage);
      handleClose();
    } catch (err) {
      console.error('❌ Errore nella creazione comanda:', err);

      let errorMessage = 'Errore nella creazione della comanda';
      if (err.detail) {
        errorMessage = err.detail;
      } else if (err.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      console.error('📢 Messaggio errore:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // Reset al passo iniziale solo se non ci sono cavi preselezionati
    if (caviPreselezionati.length > 0 && tipoComandaPreselezionato) {
      setActiveStep(2); // Torna ai dettagli se ci sono cavi preselezionati
    } else {
      setActiveStep(0); // Altrimenti torna al primo passo
    }

    setTipoComanda(tipoComandaPreselezionato || 'POSA');
    setCaviDisponibili([]);
    setCaviSelezionati(caviPreselezionati || []);
    setFormData({
      responsabile: '',
      responsabile_email: '',
      responsabile_telefono: '',
      data_scadenza: '',
      numero_componenti_squadra: 1,
      note_capo_cantiere: ''
    });
    setError(null);
    onClose && onClose();
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      case 'CERTIFICAZIONE': return 'Certificazione';
      default: return tipo;
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Seleziona il tipo di comanda
            </Typography>
            <TextField
              fullWidth
              select
              label="Tipo Comanda"
              value={tipoComanda}
              onChange={(e) => setTipoComanda(e.target.value)}
              margin="normal"
            >
              <MenuItem value="POSA">Posa</MenuItem>
              <MenuItem value="COLLEGAMENTO_PARTENZA">Collegamento Partenza</MenuItem>
              <MenuItem value="COLLEGAMENTO_ARRIVO">Collegamento Arrivo</MenuItem>
              <MenuItem value="CERTIFICAZIONE">Certificazione</MenuItem>
            </TextField>
            <Alert severity="info" sx={{ mt: 2 }}>
              <strong>{getTipoComandaLabel(tipoComanda)}</strong>: 
              {tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi'}
              {tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza'}
              {tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo'}
              {tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione e test dei cavi'}
            </Alert>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Seleziona i cavi per la comanda di {getTipoComandaLabel(tipoComanda)}
            </Typography>
            
            {loadingCavi ? (
              <Box display="flex" justifyContent="center" p={3}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Box mb={2}>
                  <Chip 
                    icon={<CableIcon />}
                    label={`${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`}
                    color={caviSelezionati.length > 0 ? 'primary' : 'default'}
                  />
                </Box>
                
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">Sel.</TableCell>
                        <TableCell>ID Cavo</TableCell>
                        <TableCell>Tipologia</TableCell>
                        <TableCell>Sezione</TableCell>
                        <TableCell>Metri</TableCell>
                        <TableCell>Partenza</TableCell>
                        <TableCell>Arrivo</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {caviDisponibili.map((cavo) => {
                        const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);
                        return (
                          <TableRow 
                            key={cavo.id_cavo}
                            hover
                            onClick={() => handleCavoToggle(cavo)}
                            sx={{ cursor: 'pointer' }}
                          >
                            <TableCell padding="checkbox">
                              <Checkbox checked={isSelected} />
                            </TableCell>
                            <TableCell>{cavo.id_cavo}</TableCell>
                            <TableCell>{cavo.tipologia}</TableCell>
                            <TableCell>{cavo.sezione}</TableCell>
                            <TableCell>{cavo.metri_teorici}</TableCell>
                            <TableCell>{cavo.ubicazione_partenza}</TableCell>
                            <TableCell>{cavo.ubicazione_arrivo}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>

                {caviDisponibili.length === 0 && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    Nessun cavo disponibile per il tipo di comanda selezionato.
                  </Alert>
                )}
              </>
            )}
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Dettagli della comanda
            </Typography>
            
            <Grid container spacing={3}>
              {/* Prima riga: Responsabile e Data Scadenza */}
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  select
                  label="Responsabile"
                  value={formData.responsabile}
                  onChange={(e) => {
                    const nomeResponsabile = e.target.value;
                    const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === nomeResponsabile);
                    if (responsabile) {
                      setFormData({
                        ...formData,
                        responsabile: nomeResponsabile,
                        responsabile_email: responsabile.email || '',
                        responsabile_telefono: responsabile.telefono || ''
                      });
                    } else {
                      setFormData({ ...formData, responsabile: nomeResponsabile });
                    }
                  }}
                  required
                  helperText="Chi eseguirà il lavoro (obbligatorio)"
                  size="medium"
                  InputProps={{
                    endAdornment: loadingResponsabili ? <CircularProgress color="inherit" size={20} /> : null
                  }}
                >
                  {responsabiliDisponibili.map((responsabile) => (
                    <MenuItem key={responsabile.id_responsabile} value={responsabile.nome_responsabile}>
                      <Box>
                        <Typography variant="body1">
                          {responsabile.nome_responsabile}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {responsabile.email && `📧 ${responsabile.email}`}
                          {responsabile.email && responsabile.telefono && ' • '}
                          {responsabile.telefono && `📞 ${responsabile.telefono}`}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Data Scadenza"
                  type="date"
                  value={formData.data_scadenza}
                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                  helperText="Scadenza prevista"
                  size="medium"
                />
              </Grid>

              {/* Seconda riga: Numero Componenti Squadra */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Numero Componenti Squadra"
                  type="number"
                  value={formData.numero_componenti_squadra}
                  onChange={(e) => setFormData({
                    ...formData,
                    numero_componenti_squadra: Math.max(1, parseInt(e.target.value) || 1)
                  })}
                  inputProps={{ min: 1, max: 20 }}
                  helperText="Persone previste per il lavoro (per calcolo resa oraria)"
                  size="medium"
                />
              </Grid>
            </Grid>

            {/* Terza riga: Note per il Responsabile */}
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Note per il Responsabile"
                  value={formData.note_capo_cantiere}
                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}
                  multiline
                  rows={3}
                  helperText="Istruzioni specifiche per il responsabile"
                  size="medium"
                  placeholder="Inserisci eventuali istruzioni specifiche, precauzioni o dettagli tecnici per l'esecuzione del lavoro..."
                />
              </Grid>
            </Grid>

            {/* Riepilogo Comanda */}
            {formData.responsabile && (
              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  📋 Riepilogo Comanda:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                  <Typography variant="body2">
                    <strong>Tipo:</strong> {getTipoComandaLabel(tipoComanda)}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Cavi selezionati:</strong> {caviSelezionati.length}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Responsabile:</strong> {formData.responsabile}
                  </Typography>
                  {formData.numero_componenti_squadra > 1 && (
                    <Typography variant="body2">
                      <strong>Squadra:</strong> {formData.numero_componenti_squadra} persone
                    </Typography>
                  )}
                  {formData.data_scadenza && (
                    <Typography variant="body2">
                      <strong>Scadenza:</strong> {new Date(formData.data_scadenza).toLocaleDateString('it-IT')}
                    </Typography>
                  )}
                </Box>

                {/* Lista cavi selezionati */}
                {caviSelezionati.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                      🔗 Cavi assegnati:
                    </Typography>
                    <Box sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: 0.5,
                      maxHeight: '120px',
                      overflowY: 'auto',
                      p: 1,
                      bgcolor: 'background.paper',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}>
                      {caviSelezionati.map((cavo, index) => (
                        <Box
                          key={cavo.id_cavo}
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            bgcolor: 'primary.light',
                            color: 'primary.contrastText',
                            px: 1,
                            py: 0.5,
                            borderRadius: 1,
                            fontSize: '0.75rem',
                            fontWeight: 'medium'
                          }}
                        >
                          {cavo.id_cavo}
                          {cavo.tipologia && (
                            <Typography variant="caption" sx={{ ml: 0.5, opacity: 0.8 }}>
                              ({cavo.tipologia})
                            </Typography>
                          )}
                        </Box>
                      ))}
                    </Box>

                    {/* Informazioni aggiuntive sui cavi */}
                    {(() => {
                      const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);
                      const tipologie = [...new Set(caviSelezionati.map(c => c.tipologia).filter(Boolean))];

                      return (
                        <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                          {metriTotali > 0 && (
                            <Typography variant="caption" color="text.secondary">
                              <strong>Metri totali:</strong> {metriTotali.toFixed(0)}m
                            </Typography>
                          )}
                          {tipologie.length > 0 && (
                            <Typography variant="caption" color="text.secondary">
                              <strong>Tipologie:</strong> {tipologie.join(', ')}
                            </Typography>
                          )}
                        </Box>
                      );
                    })()}
                  </Box>
                )}

                {/* Calcolo ore previste e resa stimata */}
                {formData.data_scadenza && formData.numero_componenti_squadra && (
                  <Box sx={{ mt: 2, p: 1.5, bgcolor: 'info.light', borderRadius: 1, color: 'info.contrastText' }}>
                    <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                      💡 Stima Lavoro:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 0.5 }}>
                      {(() => {
                        const oggi = new Date();
                        const scadenza = new Date(formData.data_scadenza);
                        const giorniDisponibili = Math.max(1, Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24)));
                        const oreGiornaliere = 8; // Ore lavorative standard
                        const oreTotaliDisponibili = giorniDisponibili * oreGiornaliere * formData.numero_componenti_squadra;

                        // Stima metri totali (approssimativa)
                        const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);

                        // Resa stimata in base al tipo di comanda
                        let resaStimata = 0;
                        let unitaMisura = '';

                        if (tipoComanda === 'POSA') {
                          resaStimata = metriTotali > 0 ? (oreTotaliDisponibili / metriTotali * formData.numero_componenti_squadra).toFixed(1) : 0;
                          unitaMisura = 'm/h per persona';
                        } else if (tipoComanda.includes('COLLEGAMENTO')) {
                          resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;
                          unitaMisura = 'h per collegamento';
                        } else if (tipoComanda === 'CERTIFICAZIONE') {
                          resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;
                          unitaMisura = 'h per test';
                        }

                        return (
                          <>
                            <Typography variant="caption">
                              <strong>Giorni disponibili:</strong> {giorniDisponibili}
                            </Typography>
                            <Typography variant="caption">
                              <strong>Ore totali squadra:</strong> {oreTotaliDisponibili}h
                            </Typography>
                            {tipoComanda === 'POSA' && metriTotali > 0 && (
                              <Typography variant="caption">
                                <strong>Metri totali:</strong> {metriTotali.toFixed(0)}m
                              </Typography>
                            )}
                            {resaStimata > 0 && (
                              <Typography variant="caption">
                                <strong>Resa richiesta:</strong> {resaStimata} {unitaMisura}
                              </Typography>
                            )}
                          </>
                        );
                      })()}
                    </Box>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <AssignmentIcon />
          Crea Nuova Comanda con Cavi
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {renderStepContent()}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, gap: 2 }}>
        {activeStep < steps.length - 1 ? (
          <>
            <Button
              onClick={handleClose}
              variant="outlined"
              sx={{ minWidth: 120 }}
            >
              Chiudi
            </Button>
            <Button
              onClick={handleNext}
              variant="contained"
              disabled={activeStep === 1 && loadingCavi}
              sx={{ minWidth: 120 }}
            >
              Avanti
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={handleClose}
              variant="outlined"
              sx={{ minWidth: 120 }}
            >
              Chiudi
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={
                loading ||
                caviSelezionati.length === 0 ||
                !formData.responsabile.trim()
              }
              startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
              sx={{ minWidth: 120 }}
            >
              {loading ? 'Creazione...' : 'Crea Comanda'}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreaComandaConCavi;

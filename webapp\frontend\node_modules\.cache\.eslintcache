[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "16", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\config.js": "19", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "20", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "21", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "29", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "30", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "31", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "32", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "33", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "34", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "37", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "42", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "43", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "47", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "49", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "56", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "59", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "60", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "61", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "62", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "63", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "64", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "65", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "70", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "72", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "73", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "74", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "87", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "88", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "89", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "94", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js": "100", "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js": "101", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js": "102", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js": "104", "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js": "107", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js": "108", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js": "109", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js": "110", "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js": "111", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js": "112", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CollegamentiDialog.js": "113"}, {"size": 557, "mtime": 1746952718482, "results": "114", "hashOfConfig": "115"}, {"size": 3563, "mtime": 1750013079249, "results": "116", "hashOfConfig": "115"}, {"size": 996, "mtime": 1746970152489, "results": "117", "hashOfConfig": "115"}, {"size": 11085, "mtime": 1749414725518, "results": "118", "hashOfConfig": "115"}, {"size": 21191, "mtime": 1748751093271, "results": "119", "hashOfConfig": "115"}, {"size": 4987, "mtime": 1749534568885, "results": "120", "hashOfConfig": "115"}, {"size": 2216, "mtime": 1746640055487, "results": "121", "hashOfConfig": "115"}, {"size": 7394, "mtime": 1748034003517, "results": "122", "hashOfConfig": "115"}, {"size": 7347, "mtime": 1749615166316, "results": "123", "hashOfConfig": "115"}, {"size": 15637, "mtime": 1749188445361, "results": "124", "hashOfConfig": "115"}, {"size": 2455, "mtime": 1749188313610, "results": "125", "hashOfConfig": "115"}, {"size": 2050, "mtime": 1746647945415, "results": "126", "hashOfConfig": "115"}, {"size": 700, "mtime": 1747545501078, "results": "127", "hashOfConfig": "115"}, {"size": 14255, "mtime": 1749366744543, "results": "128", "hashOfConfig": "115"}, {"size": 3028, "mtime": 1748816305304, "results": "129", "hashOfConfig": "115"}, {"size": 1630, "mtime": 1746336079554, "results": "130", "hashOfConfig": "115"}, {"size": 1909, "mtime": 1748722592098, "results": "131", "hashOfConfig": "115"}, {"size": 82534, "mtime": 1750096319495, "results": "132", "hashOfConfig": "115"}, {"size": 324, "mtime": 1749501001043, "results": "133", "hashOfConfig": "115"}, {"size": 9068, "mtime": 1746856425683, "results": "134", "hashOfConfig": "115"}, {"size": 2210, "mtime": 1747432283057, "results": "135", "hashOfConfig": "115"}, {"size": 4494, "mtime": 1748121063631, "results": "136", "hashOfConfig": "115"}, {"size": 53872, "mtime": 1750097247460, "results": "137", "hashOfConfig": "115"}, {"size": 3337, "mtime": 1748816346924, "results": "138", "hashOfConfig": "115"}, {"size": 2958, "mtime": 1748816316425, "results": "139", "hashOfConfig": "115"}, {"size": 3507, "mtime": 1748816326922, "results": "140", "hashOfConfig": "115"}, {"size": 3340, "mtime": 1748816336281, "results": "141", "hashOfConfig": "115"}, {"size": 6097, "mtime": 1749534549483, "results": "142", "hashOfConfig": "115"}, {"size": 5880, "mtime": 1748121404574, "results": "143", "hashOfConfig": "115"}, {"size": 3889, "mtime": 1748664890350, "results": "144", "hashOfConfig": "115"}, {"size": 4720, "mtime": 1746771178920, "results": "145", "hashOfConfig": "115"}, {"size": 7681, "mtime": 1749184406942, "results": "146", "hashOfConfig": "115"}, {"size": 10819, "mtime": 1749184481438, "results": "147", "hashOfConfig": "115"}, {"size": 6259, "mtime": 1746965906057, "results": "148", "hashOfConfig": "115"}, {"size": 4215, "mtime": 1746278746358, "results": "149", "hashOfConfig": "115"}, {"size": 1273, "mtime": 1746809069006, "results": "150", "hashOfConfig": "115"}, {"size": 14270, "mtime": 1748371983481, "results": "151", "hashOfConfig": "115"}, {"size": 2752, "mtime": 1747022186740, "results": "152", "hashOfConfig": "115"}, {"size": 1072, "mtime": 1746637929350, "results": "153", "hashOfConfig": "115"}, {"size": 6745, "mtime": 1747545492454, "results": "154", "hashOfConfig": "115"}, {"size": 539, "mtime": 1749491416504, "results": "155", "hashOfConfig": "115"}, {"size": 43883, "mtime": 1749161040576, "results": "156", "hashOfConfig": "115"}, {"size": 1947, "mtime": 1748120984640, "results": "157", "hashOfConfig": "115"}, {"size": 55218, "mtime": 1749676708816, "results": "158", "hashOfConfig": "115"}, {"size": 13911, "mtime": 1749069212408, "results": "159", "hashOfConfig": "115"}, {"size": 20923, "mtime": 1749709738306, "results": "160", "hashOfConfig": "115"}, {"size": 11835, "mtime": 1748920731807, "results": "161", "hashOfConfig": "115"}, {"size": 2211, "mtime": 1748686293878, "results": "162", "hashOfConfig": "115"}, {"size": 9215, "mtime": 1749162481509, "results": "163", "hashOfConfig": "115"}, {"size": 10993, "mtime": 1747154871546, "results": "164", "hashOfConfig": "115"}, {"size": 12217, "mtime": 1749161883257, "results": "165", "hashOfConfig": "115"}, {"size": 20081, "mtime": 1749162690470, "results": "166", "hashOfConfig": "115"}, {"size": 7032, "mtime": 1748069273238, "results": "167", "hashOfConfig": "115"}, {"size": 8589, "mtime": 1748207111023, "results": "168", "hashOfConfig": "115"}, {"size": 13653, "mtime": 1749367215461, "results": "169", "hashOfConfig": "115"}, {"size": 12817, "mtime": 1749183241975, "results": "170", "hashOfConfig": "115"}, {"size": 36555, "mtime": 1747684003188, "results": "171", "hashOfConfig": "115"}, {"size": 9128, "mtime": 1749069292534, "results": "172", "hashOfConfig": "115"}, {"size": 20387, "mtime": 1748984521895, "results": "173", "hashOfConfig": "115"}, {"size": 522, "mtime": 1747022186711, "results": "174", "hashOfConfig": "115"}, {"size": 11907, "mtime": 1749189769410, "results": "175", "hashOfConfig": "115"}, {"size": 12236, "mtime": 1749676674455, "results": "176", "hashOfConfig": "115"}, {"size": 1703, "mtime": 1746972529152, "results": "177", "hashOfConfig": "115"}, {"size": 18402, "mtime": 1749156991134, "results": "178", "hashOfConfig": "115"}, {"size": 12050, "mtime": 1747547543421, "results": "179", "hashOfConfig": "115"}, {"size": 1686, "mtime": 1746946499500, "results": "180", "hashOfConfig": "115"}, {"size": 5145, "mtime": 1746914029633, "results": "181", "hashOfConfig": "115"}, {"size": 10253, "mtime": 1749156772006, "results": "182", "hashOfConfig": "115"}, {"size": 32925, "mtime": 1749707219614, "results": "183", "hashOfConfig": "115"}, {"size": 2574, "mtime": 1748920719208, "results": "184", "hashOfConfig": "115"}, {"size": 4094, "mtime": 1748161663641, "results": "185", "hashOfConfig": "115"}, {"size": 4717, "mtime": 1749142942884, "results": "186", "hashOfConfig": "115"}, {"size": 4346, "mtime": 1747491472989, "results": "187", "hashOfConfig": "115"}, {"size": 15647, "mtime": 1748899398456, "results": "188", "hashOfConfig": "115"}, {"size": 7659, "mtime": 1749366714525, "results": "189", "hashOfConfig": "115"}, {"size": 12341, "mtime": 1749366595552, "results": "190", "hashOfConfig": "115"}, {"size": 15764, "mtime": 1748877145346, "results": "191", "hashOfConfig": "115"}, {"size": 6899, "mtime": 1748877131332, "results": "192", "hashOfConfig": "115"}, {"size": 5536, "mtime": 1748670096009, "results": "193", "hashOfConfig": "115"}, {"size": 5457, "mtime": 1748666884369, "results": "194", "hashOfConfig": "115"}, {"size": 5605, "mtime": 1748666925194, "results": "195", "hashOfConfig": "115"}, {"size": 82038, "mtime": 1749413441723, "results": "196", "hashOfConfig": "115"}, {"size": 22794, "mtime": 1749490955320, "results": "197", "hashOfConfig": "115"}, {"size": 3708, "mtime": 1748705727900, "results": "198", "hashOfConfig": "115"}, {"size": 10270, "mtime": 1748724524628, "results": "199", "hashOfConfig": "115"}, {"size": 15055, "mtime": 1748755908778, "results": "200", "hashOfConfig": "115"}, {"size": 16415, "mtime": 1748755956687, "results": "201", "hashOfConfig": "115"}, {"size": 3434, "mtime": 1748755857115, "results": "202", "hashOfConfig": "115"}, {"size": 3483, "mtime": 1748755829302, "results": "203", "hashOfConfig": "115"}, {"size": 3508, "mtime": 1748755842942, "results": "204", "hashOfConfig": "115"}, {"size": 956, "mtime": 1748878396989, "results": "205", "hashOfConfig": "115"}, {"size": 18870, "mtime": 1749710528371, "results": "206", "hashOfConfig": "115"}, {"size": 14748, "mtime": 1750097093009, "results": "207", "hashOfConfig": "115"}, {"size": 3613, "mtime": 1748921268108, "results": "208", "hashOfConfig": "115"}, {"size": 1153, "mtime": 1748921279608, "results": "209", "hashOfConfig": "115"}, {"size": 6579, "mtime": 1748922219011, "results": "210", "hashOfConfig": "115"}, {"size": 8976, "mtime": 1748922249445, "results": "211", "hashOfConfig": "115"}, {"size": 29408, "mtime": 1749154958292, "results": "212", "hashOfConfig": "115"}, {"size": 24586, "mtime": 1749155015426, "results": "213", "hashOfConfig": "115"}, {"size": 11668, "mtime": 1749366480246, "results": "214", "hashOfConfig": "115"}, {"size": 3389, "mtime": 1749417624087, "results": "215", "hashOfConfig": "115"}, {"size": 15031, "mtime": 1749439918292, "results": "216", "hashOfConfig": "115"}, {"size": 15003, "mtime": 1749419448637, "results": "217", "hashOfConfig": "115"}, {"size": 14243, "mtime": 1749419412675, "results": "218", "hashOfConfig": "115"}, {"size": 4833, "mtime": 1749487592760, "results": "219", "hashOfConfig": "115"}, {"size": 14380, "mtime": 1749489582520, "results": "220", "hashOfConfig": "115"}, {"size": 33879, "mtime": 1749711244845, "results": "221", "hashOfConfig": "115"}, {"size": 7486, "mtime": 1749556154284, "results": "222", "hashOfConfig": "115"}, {"size": 11558, "mtime": 1749710555149, "results": "223", "hashOfConfig": "115"}, {"size": 41221, "mtime": 1749620791154, "results": "224", "hashOfConfig": "115"}, {"size": 10103, "mtime": 1749620669254, "results": "225", "hashOfConfig": "115"}, {"size": 34499, "mtime": 1750021763766, "results": "226", "hashOfConfig": "115"}, {"size": 8649, "mtime": 1749711144482, "results": "227", "hashOfConfig": "115"}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["567"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["568", "569", "570", "571"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["584"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["585", "586", "587", "588", "589", "590", "591", "592", "593", "594"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", ["595"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["611"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["612", "613", "614", "615", "616", "617", "618", "619"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["653"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["654"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["655"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["656", "657", "658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["660", "661"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["662", "663"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["664", "665", "666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["668", "669"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["670", "671"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["672", "673"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["674", "675"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["718", "719", "720", "721"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["722", "723", "724", "725", "726", "727", "728", "729", "730"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["731", "732"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["744"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["767"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["768"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["769"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["781", "782", "783", "784"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["799", "800"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["801"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["802", "803", "804", "805", "806", "807"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["825", "826", "827", "828", "829"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["830"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["831", "832", "833"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["834", "835"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["836", "837"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["838"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["839"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["840"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["841", "842", "843", "844", "845", "846"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["847", "848"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["849", "850", "851", "852", "853", "854", "855", "856", "857"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["858", "859", "860", "861", "862", "863", "864", "865", "866"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["867", "868", "869", "870", "871", "872"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["873", "874", "875", "876", "877", "878"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["879", "880", "881", "882"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js", ["883", "884", "885", "886"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js", ["887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js", ["911"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js", ["912", "913", "914", "915", "916"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js", ["917"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js", ["918", "919", "920", "921", "922"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js", ["923", "924", "925"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js", ["926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js", ["937", "938", "939", "940", "941", "942", "943", "944"], ["945", "946"], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ResponsabiliListPopup.js", ["947", "948"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\TipologieCaviManager.js", ["949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\tipologieCaviService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\InserimentoMetriDialog.js", ["960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CollegamentiDialog.js", ["980"], [], {"ruleId": "981", "severity": 1, "message": "982", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 14}, {"ruleId": "985", "severity": 1, "message": "986", "line": 78, "column": 11, "nodeType": "987", "messageId": "988", "endLine": 78, "endColumn": 115}, {"ruleId": "985", "severity": 1, "message": "986", "line": 80, "column": 11, "nodeType": "987", "messageId": "988", "endLine": 80, "endColumn": 107}, {"ruleId": "985", "severity": 1, "message": "986", "line": 86, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 86, "endColumn": 105}, {"ruleId": "985", "severity": 1, "message": "986", "line": 89, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 89, "endColumn": 41}, {"ruleId": "981", "severity": 1, "message": "989", "line": 13, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "990", "line": 20, "column": 25, "nodeType": "983", "messageId": "984", "endLine": 20, "endColumn": 34}, {"ruleId": "981", "severity": 1, "message": "991", "line": 21, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 35}, {"ruleId": "981", "severity": 1, "message": "992", "line": 22, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 22, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "993", "line": 23, "column": 18, "nodeType": "983", "messageId": "984", "endLine": 23, "endColumn": 28}, {"ruleId": "981", "severity": 1, "message": "994", "line": 40, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 40, "endColumn": 35}, {"ruleId": "981", "severity": 1, "message": "995", "line": 40, "column": 37, "nodeType": "983", "messageId": "984", "endLine": 40, "endColumn": 62}, {"ruleId": "981", "severity": 1, "message": "996", "line": 57, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 57, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "997", "line": 58, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 58, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "998", "line": 59, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 59, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "999", "line": 60, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 60, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1000", "line": 69, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 69, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1001", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1002", "line": 2, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 31}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 2, "column": 33, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 37}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 2, "column": 39, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 50}, {"ruleId": "981", "severity": 1, "message": "1005", "line": 2, "column": 52, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 66}, {"ruleId": "981", "severity": 1, "message": "989", "line": 2, "column": 68, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 74}, {"ruleId": "981", "severity": 1, "message": "990", "line": 5, "column": 25, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 34}, {"ruleId": "981", "severity": 1, "message": "991", "line": 6, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 6, "endColumn": 35}, {"ruleId": "981", "severity": 1, "message": "992", "line": 7, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 7, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "993", "line": 8, "column": 18, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 28}, {"ruleId": "981", "severity": 1, "message": "1006", "line": 43, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 43, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1007", "line": 16, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "982", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1002", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1009", "line": 15, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 15, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1010", "line": 16, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1011", "line": 17, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1012", "line": 18, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 18, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1013", "line": 19, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 19, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1014", "line": 20, "column": 14, "nodeType": "983", "messageId": "984", "endLine": 20, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1015", "line": 25, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1016", "line": 28, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 28, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "1017", "line": 48, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 48, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1018", "line": 53, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 53, "endColumn": 20}, {"ruleId": "981", "severity": 1, "message": "1019", "line": 11, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1021", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1022", "line": 7, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 7, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1023", "line": 12, "column": 14, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1009", "line": 13, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1024", "line": 17, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1016", "line": 21, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "1025", "line": 26, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 26, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1022", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1026", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1027", "line": 14, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1028", "line": 20, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 20, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1029", "line": 21, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1030", "line": 22, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 22, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1031", "line": 23, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 23, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1032", "line": 26, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 26, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1033", "line": 30, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1034", "line": 32, "column": 14, "nodeType": "983", "messageId": "984", "endLine": 32, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1035", "line": 33, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1036", "line": 34, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1037", "line": 35, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 35, "endColumn": 51}, {"ruleId": "981", "severity": 1, "message": "1038", "line": 42, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 42, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1039", "line": 53, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 53, "endColumn": 24}, {"ruleId": "981", "severity": 1, "message": "1040", "line": 54, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 54, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1016", "line": 67, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 67, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "1041", "line": 68, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 32}, {"ruleId": "981", "severity": 1, "message": "994", "line": 68, "column": 34, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 58}, {"ruleId": "981", "severity": 1, "message": "1042", "line": 68, "column": 60, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 82}, {"ruleId": "981", "severity": 1, "message": "995", "line": 68, "column": 84, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 109}, {"ruleId": "981", "severity": 1, "message": "1043", "line": 68, "column": 111, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 133}, {"ruleId": "981", "severity": 1, "message": "1044", "line": 68, "column": 135, "nodeType": "983", "messageId": "984", "endLine": 68, "endColumn": 160}, {"ruleId": "981", "severity": 1, "message": "1045", "line": 69, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 69, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1025", "line": 71, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 71, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1046", "line": 291, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 291, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1047", "line": 299, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 299, "endColumn": 28}, {"ruleId": "981", "severity": 1, "message": "1048", "line": 300, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 300, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1049", "line": 300, "column": 25, "nodeType": "983", "messageId": "984", "endLine": 300, "endColumn": 41}, {"ruleId": "1050", "severity": 1, "message": "1051", "line": 662, "column": 6, "nodeType": "1052", "endLine": 662, "endColumn": 15, "suggestions": "1053"}, {"ruleId": "981", "severity": 1, "message": "1054", "line": 874, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 874, "endColumn": 33}, {"ruleId": "981", "severity": 1, "message": "1055", "line": 1, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1056", "line": 49, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 49, "endColumn": 26}, {"ruleId": "1050", "severity": 1, "message": "1057", "line": 178, "column": 6, "nodeType": "1052", "endLine": 178, "endColumn": 38, "suggestions": "1058"}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1021", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1025", "line": 26, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 26, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1059", "line": 48, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 48, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1059", "line": 37, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 37, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1059", "line": 52, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 52, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1021", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1025", "line": 26, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 26, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1059", "line": 48, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 48, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1016", "line": 24, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 24, "endColumn": 26}, {"ruleId": "1050", "severity": 1, "message": "1060", "line": 53, "column": 6, "nodeType": "1052", "endLine": 53, "endColumn": 18, "suggestions": "1061"}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 14, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1064", "line": 28, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 28, "endColumn": 18}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "982", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1065", "line": 23, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 23, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1066", "line": 24, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 24, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 25, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1022", "line": 29, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 29, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1067", "line": 30, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1068", "line": 31, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 31, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1069", "line": 32, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 32, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1070", "line": 33, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1071", "line": 34, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1072", "line": 35, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 35, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1012", "line": 39, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 39, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1010", "line": 43, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 43, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1073", "line": 44, "column": 14, "nodeType": "983", "messageId": "984", "endLine": 44, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1074", "line": 50, "column": 69, "nodeType": "983", "messageId": "984", "endLine": 50, "endColumn": 76}, {"ruleId": "981", "severity": 1, "message": "1075", "line": 79, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 79, "endColumn": 26}, {"ruleId": "1050", "severity": 1, "message": "1076", "line": 161, "column": 6, "nodeType": "1052", "endLine": 161, "endColumn": 8, "suggestions": "1077"}, {"ruleId": "981", "severity": 1, "message": "1078", "line": 675, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 675, "endColumn": 26}, {"ruleId": "985", "severity": 1, "message": "986", "line": 260, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 264, "endColumn": 11}, {"ruleId": "985", "severity": 1, "message": "986", "line": 274, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 274, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 278, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 278, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 333, "column": 11, "nodeType": "987", "messageId": "988", "endLine": 338, "endColumn": 13}, {"ruleId": "985", "severity": 1, "message": "986", "line": 435, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 439, "endColumn": 11}, {"ruleId": "985", "severity": 1, "message": "986", "line": 451, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 451, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 653, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 653, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 662, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 662, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 666, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 666, "endColumn": 54}, {"ruleId": "981", "severity": 1, "message": "1079", "line": 740, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 740, "endColumn": 22}, {"ruleId": "985", "severity": 1, "message": "986", "line": 760, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 764, "endColumn": 11}, {"ruleId": "985", "severity": 1, "message": "986", "line": 779, "column": 11, "nodeType": "987", "messageId": "988", "endLine": 783, "endColumn": 13}, {"ruleId": "985", "severity": 1, "message": "986", "line": 786, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 789, "endColumn": 11}, {"ruleId": "985", "severity": 1, "message": "986", "line": 795, "column": 11, "nodeType": "987", "messageId": "988", "endLine": 799, "endColumn": 13}, {"ruleId": "985", "severity": 1, "message": "986", "line": 802, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 805, "endColumn": 11}, {"ruleId": "985", "severity": 1, "message": "986", "line": 870, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 874, "endColumn": 11}, {"ruleId": "1080", "severity": 1, "message": "1081", "line": 940, "column": 3, "nodeType": "1082", "messageId": "1083", "endLine": 940, "endColumn": 29}, {"ruleId": "985", "severity": 1, "message": "986", "line": 1237, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 1237, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 1267, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 1267, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 1320, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 1320, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 1367, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 1367, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 1421, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 1425, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1084", "line": 6, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 6, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1085", "line": 20, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 20, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1086", "line": 205, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 205, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 2, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1087", "line": 2, "column": 64, "nodeType": "983", "messageId": "984", "endLine": 2, "endColumn": 70}, {"ruleId": "981", "severity": 1, "message": "1036", "line": 4, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1088", "line": 5, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1089", "line": 6, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 6, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "1038", "line": 7, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 7, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1090", "line": 8, "column": 16, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1091", "line": 14, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 20}, {"ruleId": "981", "severity": 1, "message": "1092", "line": 121, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 121, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1093", "line": 3, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 3, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1094", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 6}, {"ruleId": "981", "severity": 1, "message": "1095", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1096", "line": 6, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 6, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1097", "line": 7, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 7, "endColumn": 6}, {"ruleId": "981", "severity": 1, "message": "1098", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1099", "line": 36, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 36, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1100", "line": 50, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 50, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1101", "line": 64, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 64, "endColumn": 20}, {"ruleId": "981", "severity": 1, "message": "1102", "line": 88, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 88, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1103", "line": 104, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 104, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1104", "line": 3, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 3, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1096", "line": 3, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 3, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1097", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 6}, {"ruleId": "981", "severity": 1, "message": "1105", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1106", "line": 6, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 6, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1107", "line": 7, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 7, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1108", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1098", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1109", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1093", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1094", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 6}, {"ruleId": "981", "severity": 1, "message": "1095", "line": 13, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1110", "line": 14, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1111", "line": 15, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 15, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1104", "line": 16, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1026", "line": 18, "column": 40, "nodeType": "983", "messageId": "984", "endLine": 18, "endColumn": 44}, {"ruleId": "981", "severity": 1, "message": "1112", "line": 47, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 47, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1113", "line": 64, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 64, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1114", "line": 71, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 71, "endColumn": 20}, {"ruleId": "981", "severity": 1, "message": "1102", "line": 79, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 79, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1103", "line": 95, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 95, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1115", "line": 272, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 272, "endColumn": 37}, {"ruleId": "981", "severity": 1, "message": "1116", "line": 273, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 273, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1021", "line": 3, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 3, "endColumn": 8}, {"ruleId": "1050", "severity": 1, "message": "1117", "line": 60, "column": 6, "nodeType": "1052", "endLine": 60, "endColumn": 34, "suggestions": "1118"}, {"ruleId": "981", "severity": 1, "message": "1119", "line": 25, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1120", "line": 33, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1121", "line": 34, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1122", "line": 35, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 35, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1123", "line": 36, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 36, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1124", "line": 37, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 37, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1125", "line": 41, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 41, "endColumn": 20}, {"ruleId": "981", "severity": 1, "message": "1126", "line": 43, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 43, "endColumn": 34}, {"ruleId": "981", "severity": 1, "message": "1127", "line": 69, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 69, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1128", "line": 69, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 69, "endColumn": 29}, {"ruleId": "1050", "severity": 1, "message": "1129", "line": 88, "column": 6, "nodeType": "1052", "endLine": 88, "endColumn": 18, "suggestions": "1130"}, {"ruleId": "1050", "severity": 1, "message": "1131", "line": 448, "column": 6, "nodeType": "1052", "endLine": 448, "endColumn": 28, "suggestions": "1132"}, {"ruleId": "981", "severity": 1, "message": "1007", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1085", "line": 21, "column": 20, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1086", "line": 100, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 100, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1133", "line": 119, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 119, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1134", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1135", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1136", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1137", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1138", "line": 13, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1139", "line": 14, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1140", "line": 15, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 15, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "1141", "line": 36, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 36, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1142", "line": 37, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 37, "endColumn": 20}, {"ruleId": "1050", "severity": 1, "message": "1143", "line": 46, "column": 6, "nodeType": "1052", "endLine": 46, "endColumn": 18, "suggestions": "1144"}, {"ruleId": "981", "severity": 1, "message": "1145", "line": 265, "column": 23, "nodeType": "983", "messageId": "984", "endLine": 265, "endColumn": 44}, {"ruleId": "981", "severity": 1, "message": "1146", "line": 266, "column": 23, "nodeType": "983", "messageId": "984", "endLine": 266, "endColumn": 42}, {"ruleId": "981", "severity": 1, "message": "1145", "line": 381, "column": 21, "nodeType": "983", "messageId": "984", "endLine": 381, "endColumn": 42}, {"ruleId": "981", "severity": 1, "message": "1146", "line": 382, "column": 21, "nodeType": "983", "messageId": "984", "endLine": 382, "endColumn": 40}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1147", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "1080", "severity": 1, "message": "1148", "line": 127, "column": 3, "nodeType": "1082", "messageId": "1083", "endLine": 127, "endColumn": 19}, {"ruleId": "1080", "severity": 1, "message": "1149", "line": 149, "column": 3, "nodeType": "1082", "messageId": "1083", "endLine": 149, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1150", "line": 278, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 278, "endColumn": 20}, {"ruleId": "1080", "severity": 1, "message": "1151", "line": 303, "column": 3, "nodeType": "1082", "messageId": "1083", "endLine": 303, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1062", "line": 1, "column": 8, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1063", "line": 5, "column": 7, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1152", "line": 83, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 83, "endColumn": 21}, {"ruleId": "985", "severity": 1, "message": "986", "line": 109, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 109, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 123, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 123, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 127, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 127, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 212, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 212, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 226, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 226, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 230, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 230, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 271, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 271, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 280, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 280, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 284, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 284, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 320, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 320, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 324, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 324, "endColumn": 54}, {"ruleId": "985", "severity": 1, "message": "986", "line": 416, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 416, "endColumn": 163}, {"ruleId": "985", "severity": 1, "message": "986", "line": 425, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 425, "endColumn": 70}, {"ruleId": "985", "severity": 1, "message": "986", "line": 429, "column": 9, "nodeType": "987", "messageId": "988", "endLine": 429, "endColumn": 54}, {"ruleId": "981", "severity": 1, "message": "1127", "line": 60, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 60, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1128", "line": 60, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 60, "endColumn": 29}, {"ruleId": "1050", "severity": 1, "message": "1143", "line": 90, "column": 6, "nodeType": "1052", "endLine": 90, "endColumn": 32, "suggestions": "1153"}, {"ruleId": "981", "severity": 1, "message": "1154", "line": 378, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 378, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1155", "line": 478, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 478, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1156", "line": 17, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1031", "line": 16, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1030", "line": 17, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1029", "line": 19, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 19, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1032", "line": 14, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1157", "line": 43, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 43, "endColumn": 26}, {"ruleId": "981", "severity": 1, "message": "1022", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1158", "line": 25, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1159", "line": 33, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1160", "line": 3, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 3, "endColumn": 6}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1119", "line": 58, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 58, "endColumn": 27}, {"ruleId": "1050", "severity": 1, "message": "1161", "line": 140, "column": 6, "nodeType": "1052", "endLine": 140, "endColumn": 18, "suggestions": "1162"}, {"ruleId": "1050", "severity": 1, "message": "1163", "line": 145, "column": 6, "nodeType": "1052", "endLine": 145, "endColumn": 52, "suggestions": "1164"}, {"ruleId": "1050", "severity": 1, "message": "1165", "line": 150, "column": 6, "nodeType": "1052", "endLine": 150, "endColumn": 62, "suggestions": "1166"}, {"ruleId": "1050", "severity": 1, "message": "1167", "line": 155, "column": 6, "nodeType": "1052", "endLine": 155, "endColumn": 28, "suggestions": "1168"}, {"ruleId": "1050", "severity": 1, "message": "1169", "line": 164, "column": 6, "nodeType": "1052", "endLine": 164, "endColumn": 39, "suggestions": "1170"}, {"ruleId": "981", "severity": 1, "message": "1171", "line": 39, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 39, "endColumn": 23}, {"ruleId": "1050", "severity": 1, "message": "1172", "line": 71, "column": 6, "nodeType": "1052", "endLine": 71, "endColumn": 18, "suggestions": "1173"}, {"ruleId": "981", "severity": 1, "message": "1055", "line": 1, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1156", "line": 27, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 27, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1011", "line": 30, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1174", "line": 33, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "993", "line": 34, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1023", "line": 35, "column": 14, "nodeType": "983", "messageId": "984", "endLine": 35, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1156", "line": 27, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 27, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1175", "line": 28, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 28, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1176", "line": 29, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 29, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1177", "line": 30, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1011", "line": 34, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1178", "line": 37, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 37, "endColumn": 31}, {"ruleId": "1050", "severity": 1, "message": "1179", "line": 98, "column": 6, "nodeType": "1052", "endLine": 98, "endColumn": 24, "suggestions": "1180"}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1181", "line": 29, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 29, "endColumn": 15}, {"ruleId": "1050", "severity": 1, "message": "1182", "line": 87, "column": 6, "nodeType": "1052", "endLine": 87, "endColumn": 25, "suggestions": "1183"}, {"ruleId": "1050", "severity": 1, "message": "1184", "line": 94, "column": 6, "nodeType": "1052", "endLine": 94, "endColumn": 24, "suggestions": "1185"}, {"ruleId": "981", "severity": 1, "message": "1186", "line": 152, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 152, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1020", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1026", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1187", "line": 196, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 196, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1188", "line": 233, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 233, "endColumn": 24}, {"ruleId": "1050", "severity": 1, "message": "1189", "line": 389, "column": 6, "nodeType": "1052", "endLine": 389, "endColumn": 58, "suggestions": "1190"}, {"ruleId": "981", "severity": 1, "message": "1191", "line": 409, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 409, "endColumn": 28}, {"ruleId": "981", "severity": 1, "message": "1028", "line": 15, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 15, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1029", "line": 16, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1030", "line": 17, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1031", "line": 18, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 18, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1136", "line": 21, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1192", "line": 28, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 28, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1125", "line": 33, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1193", "line": 78, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 78, "endColumn": 24}, {"ruleId": "981", "severity": 1, "message": "1067", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1068", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1069", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1070", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1071", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1072", "line": 13, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1156", "line": 15, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 15, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 25, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1136", "line": 30, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1108", "line": 32, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 32, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1084", "line": 33, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1085", "line": 40, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 40, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1038", "line": 42, "column": 15, "nodeType": "983", "messageId": "984", "endLine": 42, "endColumn": 27}, {"ruleId": "981", "severity": 1, "message": "1120", "line": 50, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 50, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1121", "line": 51, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 51, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1122", "line": 52, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 52, "endColumn": 22}, {"ruleId": "981", "severity": 1, "message": "1123", "line": 53, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 53, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1124", "line": 54, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 54, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1194", "line": 55, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 55, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1195", "line": 56, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 56, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1196", "line": 57, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 57, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1125", "line": 58, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 58, "endColumn": 20}, {"ruleId": "1050", "severity": 1, "message": "1197", "line": 96, "column": 6, "nodeType": "1052", "endLine": 96, "endColumn": 32, "suggestions": "1198"}, {"ruleId": "981", "severity": 1, "message": "1199", "line": 223, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 223, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1158", "line": 21, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 29}, {"ruleId": "981", "severity": 1, "message": "1055", "line": 1, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1200", "line": 51, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 51, "endColumn": 30}, {"ruleId": "981", "severity": 1, "message": "1201", "line": 52, "column": 29, "nodeType": "983", "messageId": "984", "endLine": 52, "endColumn": 49}, {"ruleId": "981", "severity": 1, "message": "1202", "line": 242, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 242, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1203", "line": 246, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 246, "endColumn": 38}, {"ruleId": "981", "severity": 1, "message": "1204", "line": 75, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 75, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1205", "line": 123, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 123, "endColumn": 22}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 126, "column": 5, "nodeType": "1208", "messageId": "1209", "endLine": 201, "endColumn": 6}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 219, "column": 5, "nodeType": "1208", "messageId": "1209", "endLine": 279, "endColumn": 6}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 290, "column": 5, "nodeType": "1208", "messageId": "1209", "endLine": 336, "endColumn": 6}, {"ruleId": "1210", "severity": 1, "message": "1211", "line": 429, "column": 1, "nodeType": "1212", "endLine": 429, "endColumn": 47}, {"ruleId": "1213", "severity": 1, "message": "1214", "line": 146, "column": 25, "nodeType": "1215", "messageId": "1216", "endLine": 146, "endColumn": 26, "suggestions": "1217"}, {"ruleId": "1213", "severity": 1, "message": "1218", "line": 146, "column": 37, "nodeType": "1215", "messageId": "1216", "endLine": 146, "endColumn": 38, "suggestions": "1219"}, {"ruleId": "1213", "severity": 1, "message": "1220", "line": 146, "column": 39, "nodeType": "1215", "messageId": "1216", "endLine": 146, "endColumn": 40, "suggestions": "1221"}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 4, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 4, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 5, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 5, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1067", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1068", "line": 9, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 9, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1069", "line": 10, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 10, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1070", "line": 11, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 11, "endColumn": 17}, {"ruleId": "981", "severity": 1, "message": "1071", "line": 12, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 12, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1072", "line": 13, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 13, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1021", "line": 14, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 14, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 31, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 31, "endColumn": 10}, {"ruleId": "1050", "severity": 1, "message": "1222", "line": 64, "column": 6, "nodeType": "1052", "endLine": 64, "endColumn": 24, "suggestions": "1223"}, {"ruleId": "981", "severity": 1, "message": "1026", "line": 8, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 8, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1134", "line": 16, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 16, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1135", "line": 17, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 17, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1136", "line": 18, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 18, "endColumn": 15}, {"ruleId": "981", "severity": 1, "message": "1008", "line": 21, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "991", "line": 30, "column": 19, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 35}, {"ruleId": "981", "severity": 1, "message": "1224", "line": 493, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 493, "endColumn": 28}, {"ruleId": "981", "severity": 1, "message": "1225", "line": 503, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 503, "endColumn": 22}, {"ruleId": "1050", "severity": 1, "message": "1226", "line": 169, "column": 6, "nodeType": "1052", "endLine": 169, "endColumn": 18, "suggestions": "1227", "suppressions": "1228"}, {"ruleId": "1050", "severity": 1, "message": "1229", "line": 255, "column": 6, "nodeType": "1052", "endLine": 255, "endColumn": 88, "suggestions": "1230", "suppressions": "1231"}, {"ruleId": "981", "severity": 1, "message": "1232", "line": 1, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 25}, {"ruleId": "981", "severity": 1, "message": "1055", "line": 1, "column": 27, "nodeType": "983", "messageId": "984", "endLine": 1, "endColumn": 36}, {"ruleId": "981", "severity": 1, "message": "1003", "line": 29, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 29, "endColumn": 7}, {"ruleId": "981", "severity": 1, "message": "1004", "line": 30, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 30, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "982", "line": 31, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 31, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1175", "line": 32, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 32, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1176", "line": 33, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 33, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1177", "line": 34, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 34, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1178", "line": 42, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 42, "endColumn": 31}, {"ruleId": "981", "severity": 1, "message": "1233", "line": 100, "column": 10, "nodeType": "983", "messageId": "984", "endLine": 100, "endColumn": 24}, {"ruleId": "981", "severity": 1, "message": "1234", "line": 101, "column": 25, "nodeType": "983", "messageId": "984", "endLine": 101, "endColumn": 41}, {"ruleId": "1050", "severity": 1, "message": "1235", "line": 141, "column": 6, "nodeType": "1052", "endLine": 141, "endColumn": 16, "suggestions": "1236"}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 146, "column": 7, "nodeType": "1208", "messageId": "1209", "endLine": 165, "endColumn": 8}, {"ruleId": "981", "severity": 1, "message": "1022", "line": 21, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 21, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1108", "line": 22, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 22, "endColumn": 10}, {"ruleId": "981", "severity": 1, "message": "1028", "line": 24, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 24, "endColumn": 14}, {"ruleId": "981", "severity": 1, "message": "1029", "line": 25, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 25, "endColumn": 13}, {"ruleId": "981", "severity": 1, "message": "1030", "line": 26, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 26, "endColumn": 9}, {"ruleId": "981", "severity": 1, "message": "1031", "line": 27, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 27, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1175", "line": 38, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 38, "endColumn": 12}, {"ruleId": "981", "severity": 1, "message": "1176", "line": 39, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 39, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1177", "line": 40, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 40, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1237", "line": 41, "column": 3, "nodeType": "983", "messageId": "984", "endLine": 41, "endColumn": 11}, {"ruleId": "981", "severity": 1, "message": "1238", "line": 47, "column": 18, "nodeType": "983", "messageId": "984", "endLine": 47, "endColumn": 33}, {"ruleId": "981", "severity": 1, "message": "1239", "line": 50, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 50, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1032", "line": 51, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 51, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1178", "line": 52, "column": 17, "nodeType": "983", "messageId": "984", "endLine": 52, "endColumn": 31}, {"ruleId": "981", "severity": 1, "message": "1240", "line": 53, "column": 13, "nodeType": "983", "messageId": "984", "endLine": 53, "endColumn": 23}, {"ruleId": "981", "severity": 1, "message": "1241", "line": 54, "column": 12, "nodeType": "983", "messageId": "984", "endLine": 54, "endColumn": 21}, {"ruleId": "981", "severity": 1, "message": "1242", "line": 55, "column": 11, "nodeType": "983", "messageId": "984", "endLine": 55, "endColumn": 19}, {"ruleId": "981", "severity": 1, "message": "1243", "line": 78, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 78, "endColumn": 17}, {"ruleId": "1050", "severity": 1, "message": "1244", "line": 86, "column": 6, "nodeType": "1052", "endLine": 86, "endColumn": 21, "suggestions": "1245"}, {"ruleId": "981", "severity": 1, "message": "1246", "line": 442, "column": 9, "nodeType": "983", "messageId": "984", "endLine": 442, "endColumn": 28}, {"ruleId": "1050", "severity": 1, "message": "1247", "line": 46, "column": 6, "nodeType": "1052", "endLine": 46, "endColumn": 21, "suggestions": "1248"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'TextField' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'Chip' is defined but never used.", "'LinearProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'parcoCaviService' is defined but never used.", "'CavoForm' is defined but never used.", "'openEliminaCavoDialog' is assigned a value but never used.", "'openModificaCavoDialog' is assigned a value but never used.", "'openAggiungiCavoDialog' is assigned a value but never used.", "'setOpenAggiungiCavoDialog' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1249"], "'handleCreateCommandError' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array.", ["1250"], "'handleBackToCantieri' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1251"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1252"], "'renderBobineCards' is assigned a value but never used.", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "'Stack' is defined but never used.", "'CancelIcon' is defined but never used.", "'handleCancel' is assigned a value but never used.", "'Button' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'formatDate' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array.", ["1253"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1254"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1255"], "'handleBackToSelection' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1256"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'config' is defined but never used.", "Duplicate key 'aggiornaDatiPosa'.", "Duplicate key 'aggiornaDatiCollegamento'.", "'payload' is assigned a value but never used.", "Duplicate key 'aggiornaDatiCertificazione'.", "'sentData' is assigned a value but never used.", ["1257"], "'result' is assigned a value but never used.", "'hasMetri' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1258"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1259"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1260"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1261"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1262"], "'PeopleIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1263"], "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1264"], "'Autocomplete' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1265"], "React Hook useEffect has a missing dependency: 'loadResponsabiliDisponibili'. Either include it or remove the dependency array.", ["1266"], "'handleBack' is assigned a value but never used.", "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1267"], "'getSearchTermsCount' is assigned a value but never used.", "'CloseIcon' is defined but never used.", "'getBobinaNumber' is assigned a value but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1268"], "'bobina' is assigned a value but never used.", "'showValidationDialog' is assigned a value but never used.", "'setValidationLoading' is assigned a value but never used.", "'handleValidationDialogClose' is assigned a value but never used.", "'handleValidationDialogProceed' is assigned a value but never used.", "'getSeverityColor' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["1269", "1270"], "Unnecessary escape character: \\(.", ["1271", "1272"], "Unnecessary escape character: \\).", ["1273", "1274"], "React Hook useEffect has a missing dependency: 'loadResponsabili'. Either include it or remove the dependency array.", ["1275"], "'getTipoComandaLabel' is assigned a value but never used.", "'getStatoColor' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadComande', 'loadResponsabili', and 'loadStatistiche'. Either include them or remove the dependency array.", ["1276"], ["1277"], "React Hook useEffect has missing dependencies: 'loadResponsabili', 'searchingComanda', and 'setSearchParams'. Either include them or remove the dependency array.", ["1278"], ["1279"], "'useState' is defined but never used.", "'tipologieTotal' is assigned a value but never used.", "'setTipologiePage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["1280"], "'Collapse' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'BuildIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'isTablet' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobineDisponibili' and 'loadCaviComanda'. Either include them or remove the dependency array.", ["1281"], "'isBobinaCompatibile' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCaviComanda'. Either include it or remove the dependency array.", ["1282"], {"desc": "1283", "fix": "1284"}, {"desc": "1285", "fix": "1286"}, {"desc": "1287", "fix": "1288"}, {"desc": "1289", "fix": "1290"}, {"desc": "1291", "fix": "1292"}, {"desc": "1293", "fix": "1294"}, {"desc": "1295", "fix": "1296"}, {"desc": "1297", "fix": "1298"}, {"desc": "1299", "fix": "1300"}, {"desc": "1301", "fix": "1302"}, {"desc": "1303", "fix": "1304"}, {"desc": "1305", "fix": "1306"}, {"desc": "1307", "fix": "1308"}, {"desc": "1309", "fix": "1310"}, {"desc": "1311", "fix": "1312"}, {"desc": "1313", "fix": "1314"}, {"desc": "1315", "fix": "1316"}, {"desc": "1317", "fix": "1318"}, {"desc": "1319", "fix": "1320"}, {"desc": "1321", "fix": "1322"}, {"messageId": "1323", "fix": "1324", "desc": "1325"}, {"messageId": "1326", "fix": "1327", "desc": "1328"}, {"messageId": "1323", "fix": "1329", "desc": "1325"}, {"messageId": "1326", "fix": "1330", "desc": "1328"}, {"messageId": "1323", "fix": "1331", "desc": "1325"}, {"messageId": "1326", "fix": "1332", "desc": "1328"}, {"desc": "1333", "fix": "1334"}, {"desc": "1335", "fix": "1336"}, {"kind": "1337", "justification": "1338"}, {"desc": "1339", "fix": "1340"}, {"kind": "1337", "justification": "1338"}, {"desc": "1341", "fix": "1342"}, {"desc": "1343", "fix": "1344"}, {"desc": "1345", "fix": "1346"}, "Update the dependencies array to be: [calculateStatistics, caviAttivi, caviSpare, error, filters, user]", {"range": "1347", "text": "1348"}, "Update the dependencies array to be: [cantiereId, loadStoricoBobine, selectedReportType]", {"range": "1349", "text": "1350"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1351", "text": "1352"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1353", "text": "1354"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi, loadWeatherData]", {"range": "1355", "text": "1356"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1357", "text": "1358"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1359", "text": "1360"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1361", "text": "1362"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1363", "text": "1364"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1365", "text": "1366"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1367", "text": "1368"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1369", "text": "1370"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1371", "text": "1372"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1373", "text": "1374"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1375", "text": "1376"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1377", "text": "1378"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1379", "text": "1380"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabiliDisponibili]", {"range": "1381", "text": "1382"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1383", "text": "1384"}, "Update the dependencies array to be: [open, cavoPreselezionato, loadBobine]", {"range": "1385", "text": "1386"}, "removeEscape", {"range": "1387", "text": "1338"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1388", "text": "1389"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1390", "text": "1338"}, {"range": "1391", "text": "1389"}, {"range": "1392", "text": "1338"}, {"range": "1393", "text": "1389"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabili]", {"range": "1394", "text": "1395"}, "Update the dependencies array to be: [cantiereId, loadComande, loadResponsabili, loadStatistiche]", {"range": "1396", "text": "1397"}, "directive", "", "Update the dependencies array to be: [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", {"range": "1398", "text": "1399"}, "Update the dependencies array to be: [loadData, tabValue]", {"range": "1400", "text": "1401"}, "Update the dependencies array to be: [open, comanda, loadCaviComanda, loadBobineDisponibili]", {"range": "1402", "text": "1403"}, "Update the dependencies array to be: [open, comanda, loadCaviComanda]", {"range": "1404", "text": "1405"}, [25892, 25901], "[calculateStatistics, caviAttivi, caviSpare, error, filters, user]", [5510, 5542], "[cantiere<PERSON>d, loadStoricoBobine, selectedReportType]", [1559, 1571], "[cantiereId, selectCantiere]", [5793, 5795], "[handleOptionSelect, initialOption, loadBobine]", [1809, 1837], "[certificazione, cantiereId, loadCavi, loadWeatherData]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [2734, 2760], "[open, bobina, cantiereId, loadCavi]", [3803, 3815], "[cantiereId, loadInitialData]", [3900, 3946], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [4030, 4086], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [4192, 4214], "[calculateStatistics, cavi, certificazioni]", [4436, 4469], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1672, 1684], "[cantiereId, loadComande, loadStatistiche]", [2516, 2534], "[certificazioneId, loadProve]", [2142, 2161], "[loadCaviDisponibili, open, tipoComanda]", [2325, 2343], "[open, cantiereId, loadResponsabiliDisponibili]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", [2440, 2466], "[open, cavoPreselezionato, loadBobine]", [4732, 4733], [4732, 4732], "\\", [4744, 4745], [4744, 4744], [4746, 4747], [4746, 4746], [1518, 1536], "[open, cantiereId, loadResponsabili]", [5745, 5757], "[cantiereId, loadComande, loadResponsabili, loadStatistiche]", [9328, 9410], "[searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili, searchingComanda, setSearchParams, loadResponsabili]", [3615, 3625], "[loadData, tabValue]", [1924, 1939], "[open, comanda, loadCaviComanda, loadBobineDisponibili]", [892, 907], "[open, comanda, loadCaviComanda]"]
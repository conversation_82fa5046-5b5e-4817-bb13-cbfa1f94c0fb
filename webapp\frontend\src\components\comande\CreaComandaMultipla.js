import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  MenuItem,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Build as BuildIcon,
  Link as LinkIcon,
  Verified as VerifiedIcon,
  Cable as CableIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
// import comandeValidationService from '../../services/comandeValidationService';
// import ValidationResultsDialog from './ValidationResultsDialog';

/**
 * Componente per la creazione rapida di comande multiple
 * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione
 */
const CreaComandaMultipla = ({ 
  open, 
  onClose, 
  onSuccess, 
  onError,
  tipoComanda,
  caviSelezionati = [],
  cantiereId 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Stati per la validazione
  const [validationResult, setValidationResult] = useState(null);
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const [validationLoading, setValidationLoading] = useState(false);

  // Dati del form
  const [formData, setFormData] = useState({
    responsabile: '',
    data_scadenza: '',
    numero_componenti_squadra: 1,
    note_capo_cantiere: ''
  });

  // Funzione per ottenere l'etichetta del tipo comanda
  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      case 'CERTIFICAZIONE': return 'Certificazione';
      default: return tipo;
    }
  };

  // Funzione per ottenere l'icona del tipo comanda
  const getTipoComandaIcon = (tipo) => {
    switch (tipo) {
      case 'POSA': return <BuildIcon />;
      case 'COLLEGAMENTO_PARTENZA': return <LinkIcon />;
      case 'COLLEGAMENTO_ARRIVO': return <LinkIcon />;
      case 'CERTIFICAZIONE': return <VerifiedIcon />;
      default: return <AssignmentIcon />;
    }
  };

  // Funzione per ottenere la descrizione del tipo comanda
  const getTipoComandaDescription = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Comanda per la posa fisica dei cavi';
      case 'COLLEGAMENTO_PARTENZA': return 'Comanda per il collegamento lato partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Comanda per il collegamento lato arrivo';
      case 'CERTIFICAZIONE': return 'Comanda per la certificazione e test dei cavi';
      default: return 'Comanda generica';
    }
  };

  // Validazione automatica quando cambiano i parametri - temporaneamente disabilitata
  /*
  useEffect(() => {
    if (open && caviSelezionati.length > 0 && tipoComanda && formData.responsabile.trim()) {
      performValidation();
    }
  }, [open, caviSelezionati, tipoComanda, formData.responsabile]);
  */

  // Funzione per eseguire la validazione - temporaneamente disabilitata
  const performValidation = async () => {
    // Temporaneamente disabilitata
    return;
    /*
    if (!formData.responsabile.trim()) return;

    setValidationLoading(true);
    try {
      const result = comandeValidationService.validateCaviForComanda(
        caviSelezionati,
        tipoComanda,
        formData.responsabile
      );
      setValidationResult(result);
    } catch (err) {
      console.error('Errore durante la validazione:', err);
      setError('Errore durante la validazione dei cavi');
    } finally {
      setValidationLoading(false);
    }
    */
  };

  // Reset del form quando si chiude il dialog
  const handleClose = () => {
    setFormData({
      responsabile: '',
      data_scadenza: '',
      numero_componenti_squadra: 1,
      note_capo_cantiere: ''
    });
    setError(null);
    setValidationResult(null);
    setShowValidationDialog(false);
    onClose();
  };

  // Gestione del submit con validazione
  const handleSubmit = async () => {
    try {
      // Validazione base
      if (!formData.responsabile.trim()) {
        setError('Il responsabile è obbligatorio');
        return;
      }

      if (caviSelezionati.length === 0) {
        setError('Nessun cavo selezionato');
        return;
      }

      // Esegui validazione se non già presente
      if (!validationResult) {
        await performValidation();
        return; // La validazione triggerà un re-render
      }

      // Se ci sono errori bloccanti, mostra il dialog di validazione
      if (!validationResult.valid) {
        setShowValidationDialog(true);
        return;
      }

      // Se ci sono warning, mostra il dialog per conferma
      if (validationResult.warnings.length > 0) {
        setShowValidationDialog(true);
        return;
      }

      // Procedi con la creazione
      await createComanda();
    } catch (err) {
      console.error('Errore durante il submit:', err);
      setError('Errore durante la validazione o creazione della comanda');
    }
  };

  // Funzione separata per la creazione della comanda
  const createComanda = async () => {
    try {
      setLoading(true);
      setError(null);

      // Usa solo i cavi validi se la validazione è stata eseguita
      const caviDaUsare = validationResult ? validationResult.caviValidi : caviSelezionati;

      // Prepara i dati della comanda
      const comandaData = {
        tipo_comanda: tipoComanda,
        descrizione: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviDaUsare.length} cavi`,
        responsabile: formData.responsabile,
        data_scadenza: formData.data_scadenza || null,
        numero_componenti_squadra: formData.numero_componenti_squadra || 1,
        note_capo_cantiere: formData.note_capo_cantiere
      };

      // Lista degli ID dei cavi validi
      const listaIdCavi = caviDaUsare.map(c => c.id_cavo);

      console.log('Creazione comanda multipla:', {
        cantiereId,
        comandaData,
        listaIdCavi,
        caviOriginali: caviSelezionati.length,
        caviValidi: caviDaUsare.length
      });

      // Crea la comanda con i cavi
      const response = await comandeService.createComandaConCavi(
        cantiereId,
        comandaData,
        listaIdCavi
      );

      console.log('Comanda creata con successo:', response);

      if (onSuccess) {
        onSuccess(response);
      }

      handleClose();
    } catch (err) {
      console.error('Errore nella creazione della comanda:', err);
      const errorMessage = err?.detail || err?.message || 'Errore nella creazione della comanda';
      setError(errorMessage);

      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // Gestione del dialog di validazione
  const handleValidationDialogClose = () => {
    setShowValidationDialog(false);
  };

  const handleValidationDialogProceed = async () => {
    setShowValidationDialog(false);
    await createComanda();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          {getTipoComandaIcon(tipoComanda)}
          Crea Comanda {getTipoComandaLabel(tipoComanda)}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* Informazioni tipo comanda */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="subtitle2">
              {getTipoComandaDescription(tipoComanda)}
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Verranno assegnati <strong>{caviSelezionati.length} cavi</strong> a questa comanda
            </Typography>
          </Alert>

          {/* Indicatori di validazione */}
          {validationLoading && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Box display="flex" alignItems="center" gap={1}>
                <CircularProgress size={16} />
                <Typography variant="body2">
                  Validazione cavi in corso...
                </Typography>
              </Box>
            </Alert>
          )}

          {validationResult && !validationLoading && (
            <Alert
              severity={
                !validationResult.valid ? 'error' :
                validationResult.warnings.length > 0 ? 'warning' : 'success'
              }
              sx={{ mb: 2 }}
              action={
                (validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
                  <Button
                    color="inherit"
                    size="small"
                    startIcon={<SecurityIcon />}
                    onClick={() => setShowValidationDialog(true)}
                  >
                    Dettagli
                  </Button>
                )
              }
            >
              <Typography variant="subtitle2">
                {!validationResult.valid ? 'Errori di Validazione' :
                 validationResult.warnings.length > 0 ? 'Avvisi di Validazione' : 'Validazione Completata'}
              </Typography>
              <Typography variant="body2">
                {validationResult.caviValidi.length} cavi validi
                {validationResult.caviProblematici.length > 0 &&
                  `, ${validationResult.caviProblematici.length} con problemi`}
                {validationResult.errors.length > 0 &&
                  ` (${validationResult.errors.length} errori)`}
                {validationResult.warnings.length > 0 &&
                  ` (${validationResult.warnings.length} avvisi)`}
              </Typography>
            </Alert>
          )}

          {/* Form dati comanda */}
          <Grid container spacing={3}>
            {/* Prima riga: Responsabile e Data Scadenza */}
            <Grid item xs={12} sm={8}>
              <TextField
                fullWidth
                label="Responsabile"
                value={formData.responsabile}
                onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}
                required
                helperText="Chi eseguirà il lavoro (obbligatorio)"
                size="medium"
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Data Scadenza"
                type="date"
                value={formData.data_scadenza}
                onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}
                InputLabelProps={{ shrink: true }}
                size="medium"
                helperText="Scadenza prevista"
              />
            </Grid>

            {/* Seconda riga: Numero Componenti Squadra */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Numero Componenti Squadra"
                type="number"
                value={formData.numero_componenti_squadra}
                onChange={(e) => setFormData({
                  ...formData,
                  numero_componenti_squadra: Math.max(1, parseInt(e.target.value) || 1)
                })}
                inputProps={{ min: 1, max: 20 }}
                helperText="Persone previste per il lavoro (per calcolo resa oraria)"
                size="medium"
              />
            </Grid>

            {/* Terza riga: Note per il Responsabile */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Note per il Responsabile"
                value={formData.note_capo_cantiere}
                onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}
                multiline
                rows={3}
                helperText="Istruzioni specifiche per il responsabile"
                size="medium"
                placeholder="Inserisci eventuali istruzioni specifiche, precauzioni o dettagli tecnici per l'esecuzione del lavoro..."
              />
            </Grid>
          </Grid>

          {/* Riepilogo Comanda */}
          {formData.responsabile && (
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                📋 Riepilogo Comanda:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                <Typography variant="body2">
                  <strong>Tipo:</strong> {getTipoComandaLabel(tipoComanda)}
                </Typography>
                <Typography variant="body2">
                  <strong>Cavi selezionati:</strong> {caviSelezionati.length}
                </Typography>
                <Typography variant="body2">
                  <strong>Responsabile:</strong> {formData.responsabile}
                </Typography>
                {formData.numero_componenti_squadra > 1 && (
                  <Typography variant="body2">
                    <strong>Squadra:</strong> {formData.numero_componenti_squadra} persone
                  </Typography>
                )}
                {formData.data_scadenza && (
                  <Typography variant="body2">
                    <strong>Scadenza:</strong> {new Date(formData.data_scadenza).toLocaleDateString('it-IT')}
                  </Typography>
                )}
              </Box>

              {/* Calcolo ore previste e resa stimata */}
              {formData.data_scadenza && formData.numero_componenti_squadra && (
                <Box sx={{ mt: 2, p: 1.5, bgcolor: 'info.light', borderRadius: 1, color: 'info.contrastText' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                    💡 Stima Lavoro:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 0.5 }}>
                    {(() => {
                      const oggi = new Date();
                      const scadenza = new Date(formData.data_scadenza);
                      const giorniDisponibili = Math.max(1, Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24)));
                      const oreGiornaliere = 8; // Ore lavorative standard
                      const oreTotaliDisponibili = giorniDisponibili * oreGiornaliere * formData.numero_componenti_squadra;

                      // Stima metri totali (approssimativa)
                      const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);

                      // Resa stimata in base al tipo di comanda
                      let resaStimata = 0;
                      let unitaMisura = '';

                      if (tipoComanda === 'POSA') {
                        resaStimata = metriTotali > 0 ? (oreTotaliDisponibili / metriTotali * formData.numero_componenti_squadra).toFixed(1) : 0;
                        unitaMisura = 'm/h per persona';
                      } else if (tipoComanda.includes('COLLEGAMENTO')) {
                        resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;
                        unitaMisura = 'h per collegamento';
                      } else if (tipoComanda === 'CERTIFICAZIONE') {
                        resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;
                        unitaMisura = 'h per test';
                      }

                      return (
                        <>
                          <Typography variant="caption">
                            <strong>Giorni disponibili:</strong> {giorniDisponibili}
                          </Typography>
                          <Typography variant="caption">
                            <strong>Ore totali squadra:</strong> {oreTotaliDisponibili}h
                          </Typography>
                          {tipoComanda === 'POSA' && metriTotali > 0 && (
                            <Typography variant="caption">
                              <strong>Metri totali:</strong> {metriTotali.toFixed(0)}m
                            </Typography>
                          )}
                          {resaStimata > 0 && (
                            <Typography variant="caption">
                              <strong>Resa richiesta:</strong> {resaStimata} {unitaMisura}
                            </Typography>
                          )}
                        </>
                      );
                    })()}
                  </Box>
                </Box>
              )}
            </Box>
          )}

          {/* Lista cavi selezionati */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              <CableIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Cavi Selezionati ({caviSelezionati.length})
            </Typography>
            <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <List dense>
                {caviSelezionati.map((cavo, index) => (
                  <React.Fragment key={cavo.id_cavo}>
                    <ListItem>
                      <ListItemText
                        primary={cavo.id_cavo}
                        secondary={
                          <Box>
                            <Typography variant="caption" component="span">
                              {cavo.tipologia} • {cavo.sezione} • {cavo.metri_teorici}m
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              {cavo.ubicazione_partenza} → {cavo.ubicazione_arrivo}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip 
                        size="small" 
                        label={cavo.stato_installazione || 'N/A'} 
                        variant="outlined"
                      />
                    </ListItem>
                    {index < caviSelezionati.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Box>

          {/* Errore */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Annulla
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={
            loading ||
            validationLoading ||
            !formData.responsabile.trim() ||
            caviSelezionati.length === 0 ||
            (validationResult && !validationResult.valid)
          }
          startIcon={loading ? <CircularProgress size={20} /> : getTipoComandaIcon(tipoComanda)}
        >
          {loading ? 'Creazione...' :
           validationLoading ? 'Validazione...' :
           `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`}
        </Button>
      </DialogActions>

      {/* Dialog di validazione - temporaneamente disabilitato */}
      {/*
      <ValidationResultsDialog
        open={showValidationDialog}
        onClose={handleValidationDialogClose}
        onProceed={handleValidationDialogProceed}
        validationResult={validationResult}
        tipoComanda={tipoComanda}
        responsabile={formData.responsabile}
      />
      */}
    </Dialog>
  );
};

export default CreaComandaMultipla;
